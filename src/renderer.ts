interface VersionInfo {
  firmwareName: string;
  mainVer: number;
  minorVer: number;
  internalVer: number;
}

interface DeviceInfo {
  // 网络信息
  ip: string;
  mask: string;
  connectedIp: string;
  macAddr: string;
  
  // 设备信息
  deviceId: number;
  deviceType: number;
  partNumber: number;
  status: number;
  
  // 版本信息
  fpgaVer: VersionInfo;
  armVer: VersionInfo;
  mcuVer: VersionInfo;
  
  // 传感器数据
  temperatures: number[];
  pressure: number;
  
  // 系统信息
  lastSeen: Date;
}

interface ElectronAPI {
  getDevices: () => Promise<DeviceInfo[]>;
  connectToDevice: (ip: string) => Promise<boolean>;
  disconnectFromDevice: (ip: string) => Promise<void>;
}

declare const electronAPI: ElectronAPI;

// Add type for button elements
interface HTMLButtonElement {
  getAttribute(name: string): string | null;
}

// Track expanded device details
const expandedDevices = new Set<string>();

function formatTimestamp(date: Date): string {
  return date.toLocaleString();
}

function formatVersion(ver: VersionInfo): string {
  // For ARM version, format as x.y.0, for others use internal version
  if (ver.firmwareName === 'ARM') {
    return `${ver.firmwareName} v${ver.mainVer}.${ver.minorVer}.0`;
  }
  return `${ver.firmwareName} v${ver.mainVer}.${ver.minorVer}.${ver.internalVer}`;
}

function formatTemperatures(temps: number[]): string {
  return temps.map((temp, i) => `T${i}: ${temp.toFixed(1)}°C`).join(', ');
}

function renderDeviceList(devices: DeviceInfo[]): void {
  const deviceList = document.getElementById('deviceList');
  if (!deviceList) return;

  if (devices.length === 0) {
    deviceList.innerHTML = '<p class="text-gray-400 text-center">No devices found on the network.</p>';
    return;
  }

  const deviceCards = devices.map(device => {
    const isConnected = connectedDevices.has(device.ip);
    const connectButtonClass = isConnected 
      ? 'connect-btn bg-gray-500 cursor-not-allowed text-white px-4 py-2 rounded-lg text-xs'
      : 'connect-btn bg-gradient-to-r from-green-600 to-green-700 hover:from-green-700 hover:to-green-800 text-white px-4 py-2 rounded-lg text-xs transition-all duration-200 shadow-lg hover:shadow-green-500/25';
    const connectButtonText = isConnected ? '已连接' : '连接';
    
    return `
    <div class="device-card rounded-xl p-4 mb-4 ${isConnected ? 'connected' : ''}">
      <div class="space-y-3">
        <div class="flex items-center justify-between">
          <h3 class="text-sm font-semibold text-white flex items-center">
            <span class="w-2 h-2 status-online rounded-full mr-2"></span>
            设备 ${device.deviceId}
          </h3>
          <span class="text-xs text-green-400 font-medium px-2 py-1 bg-green-900/30 rounded-full">在线</span>
        </div>
        
        <div class="text-xs text-gray-300 space-y-1">
          <div class="flex items-center">
            <span class="text-gray-400 w-8">IP:</span>
            <span class="font-mono">${device.ip}</span>
          </div>
          <div class="flex items-center">
            <span class="text-gray-400 w-8">MAC:</span>
            <span class="font-mono">${device.macAddr.toUpperCase().substring(0, 8)}...</span>
          </div>
        </div>
        
        <div class="flex items-center justify-between pt-2">
          <div class="text-xs text-gray-400">
            ${formatTimestamp(new Date(device.lastSeen)).split(' ')[1]}
          </div>
          <button class="${connectButtonClass}" data-ip="${device.ip}" data-device-id="${device.deviceId}" ${isConnected ? 'disabled' : ''}>
            ${connectButtonText}
          </button>
        </div>
        
        <!-- 可展开的详细信息 -->
        <div class="device-details ${expandedDevices.has(device.ip) ? '' : 'hidden'} border-t border-gray-600 pt-3 mt-3">
          <div class="grid grid-cols-1 gap-4 text-xs">
            <!-- 网络信息 -->
            <div class="bg-gray-700/50 rounded-lg p-3">
              <h4 class="font-medium text-blue-400 mb-2 flex items-center">
                <span class="w-1 h-1 bg-blue-400 rounded-full mr-2"></span>
                网络信息
              </h4>
              <div class="space-y-1 text-gray-300">
                <div class="flex justify-between">
                  <span class="text-gray-400">子网掩码:</span>
                  <span class="font-mono">${device.mask}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-400">连接IP:</span>
                  <span class="font-mono">${device.connectedIp === '0.0.0.0' ? '无连接' : device.connectedIp}</span>
                </div>
              </div>
            </div>
            
            <!-- 设备信息 -->
            <div class="bg-gray-700/50 rounded-lg p-3">
              <h4 class="font-medium text-purple-400 mb-2 flex items-center">
                <span class="w-1 h-1 bg-purple-400 rounded-full mr-2"></span>
                设备信息
              </h4>
              <div class="space-y-1 text-gray-300">
                <div class="flex justify-between">
                  <span class="text-gray-400">设备类型:</span>
                  <span>${device.deviceType}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-400">部件号:</span>
                  <span>${device.partNumber}</span>
                </div>
                <div class="flex justify-between">
                  <span class="text-gray-400">状态:</span>
                  <span>${device.status}</span>
                </div>
              </div>
            </div>
            
            <!-- 版本信息 -->
            <div class="bg-gray-700/50 rounded-lg p-3">
              <h4 class="font-medium text-cyan-400 mb-2 flex items-center">
                <span class="w-1 h-1 bg-cyan-400 rounded-full mr-2"></span>
                版本信息
              </h4>
              <div class="space-y-1 text-gray-300 font-mono text-xs">
                <div>${formatVersion(device.fpgaVer)}</div>
                <div>${formatVersion(device.armVer)}</div>
                <div>${formatVersion(device.mcuVer)}</div>
              </div>
            </div>
            
            <!-- 传感器数据 -->
            <div class="bg-gray-700/50 rounded-lg p-3">
              <h4 class="font-medium text-yellow-400 mb-2 flex items-center">
                <span class="w-1 h-1 bg-yellow-400 rounded-full mr-2"></span>
                传感器数据
              </h4>
              <div class="space-y-1 text-gray-300">
                <div class="flex justify-between">
                  <span class="text-gray-400">压力:</span>
                  <span class="font-mono">${device.pressure.toFixed(2)} Pa</span>
                </div>
                <div>
                  <span class="text-gray-400 block mb-1">温度:</span>
                  <span class="font-mono text-xs">${formatTemperatures(device.temperatures)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- 展开/折叠按钮 -->
        <button class="toggle-details w-full text-xs text-blue-400 hover:text-blue-300 mt-3 py-2 border border-gray-600 rounded-lg hover:bg-gray-700/30 transition-all duration-200" data-ip="${device.ip}">
          ${expandedDevices.has(device.ip) ? '隐藏详情' : '显示详情'}
        </button>
      </div>
    </div>
  `;
  }).join('');

  deviceList.innerHTML = deviceCards;
  
  // Add event listeners to connect buttons
  const connectButtons = document.querySelectorAll('.connect-btn');
  connectButtons.forEach(button => {
    button.addEventListener('click', handleConnectClick);
  });
  
  // Add event listeners to toggle details buttons
  const toggleButtons = document.querySelectorAll('.toggle-details');
  toggleButtons.forEach(button => {
    button.addEventListener('click', handleToggleDetails);
  });
}

function handleToggleDetails(event: Event): void {
  const button = event.target as HTMLButtonElement;
  const ip = button.getAttribute('data-ip');
  if (!ip) return;
  
  const deviceCard = button.closest('.device-card');
  if (!deviceCard) return;
  
  const details = deviceCard.querySelector('.device-details');
  if (!details) return;
  
  if (details.classList.contains('hidden')) {
    details.classList.remove('hidden');
    button.textContent = '隐藏详情';
    expandedDevices.add(ip);
  } else {
    details.classList.add('hidden');
    button.textContent = '显示详情';
    expandedDevices.delete(ip);
  }
}

async function refreshDevices(): Promise<void> {
  const refreshBtn = document.getElementById('refreshBtn') as HTMLButtonElement;
  if (refreshBtn) {
    refreshBtn.disabled = true;
    refreshBtn.textContent = 'Refreshing...';
  }

  try {
    const devices = await electronAPI.getDevices();
    renderDeviceList(devices);
  } catch (error) {
    console.error('Error fetching devices:', error);
    const deviceList = document.getElementById('deviceList');
    if (deviceList) {
      deviceList.innerHTML = '<p class="text-red-400 text-center">Error loading devices. Please try again.</p>';
    }
  } finally {
    if (refreshBtn) {
      refreshBtn.disabled = false;
      refreshBtn.textContent = 'Refresh Devices';
    }
  }
}

// Connected device interface
interface ConnectedDevice {
  ip: string;
  deviceId: string;
  deviceName: string;
  connectedAt: Date;
  tileId: string;
  status: 'connecting' | 'connected' | 'error';
}

// Multi-device connection management
const connectedDevices = new Map<string, ConnectedDevice>();
let nextTileId = 1;

async function handleConnectClick(event: Event): Promise<void> {
  const button = event.target as HTMLButtonElement;
  const ip = button.getAttribute('data-ip');
  const deviceId = button.getAttribute('data-device-id');
  
  if (!ip || !deviceId) return;
  
  // Check if device is already connected
  if (connectedDevices.has(ip)) {
    console.log(`Device ${ip} is already connected`);
    return;
  }
  
  try {
    button.disabled = true;
    button.textContent = '连接中...';
    
    // Create new tile for this device
    const tileId = `tile-${nextTileId++}`;
    const deviceName = `设备 ${deviceId}`;
    
    const connectedDevice: ConnectedDevice = {
      ip,
      deviceId,
      deviceName,
      connectedAt: new Date(),
      tileId,
      status: 'connecting'
    };
    
    connectedDevices.set(ip, connectedDevice);
    
    // Show tiles panel and create tile
    showTilesPanel();
    createTile(connectedDevice);
    showTileLoading(connectedDevice.tileId); // Show loading state initially
    updateConnectionCount();
    
    // Connect to device
    const success = await electronAPI.connectToDevice(ip);
    
    if (success) {
      // Load iframe in tile
      try {
        await loadSonarTile(connectedDevice);
        connectedDevice.status = 'connected';
        updateConnectButtonState(ip); // Update button state on success
        console.log(`设备 ${deviceId} (${ip}) 连接成功并加载完成`);
      } catch (loadError) {
        const errorMessage = loadError instanceof Error ? loadError.message : String(loadError);
        console.warn(`设备连接成功但界面加载出现问题: ${errorMessage}`);
        console.log(`由于主进程已验证连接成功且MJPEG流正常，强制显示iframe`);
        // Even if iframe loading has issues, the device connection was successful
        // So we still mark it as connected and show the iframe
        connectedDevice.status = 'connected';
        showTileIframe(connectedDevice.tileId);
        updateConnectButtonState(ip);

        // Force set iframe src again as a retry
        const tile = document.getElementById(connectedDevice.tileId);
        if (tile) {
          const iframe = tile.querySelector('.tile-iframe') as HTMLIFrameElement;
          if (iframe) {
            const url = `http://${connectedDevice.ip}/?autostart=yes`;
            console.log(`重新设置iframe源: ${url}`);
            iframe.src = url;
          }
        }
      }
    } else {
      throw new Error('Connection failed');
    }
  } catch (error) {
    console.error('Error connecting to device:', error);
    const device = connectedDevices.get(ip);
    if (device) {
      device.status = 'error';
      showTileError(device.tileId);
      // Remove failed connection from map
      connectedDevices.delete(ip);
      updateConnectionCount();
      
      // Remove the tile since connection failed
      const tile = document.getElementById(device.tileId);
      if (tile) {
        tile.remove();
      }
      
      // Hide tiles panel if no connections
      if (connectedDevices.size === 0) {
        hideTilesPanel();
      }
    }
  } finally {
    // Only reset button if device is not actually connected
    if (!connectedDevices.has(ip)) {
      button.disabled = false;
      button.textContent = '连接';
    }
    updateConnectButtonState(ip);
  }
}

function showTilesPanel(): void {
  const tilesPanel = document.getElementById('tilesPanel');
  const devicePanel = document.getElementById('devicePanel');
  const app = document.getElementById('app');
  
  if (tilesPanel && devicePanel && app) {
    // Add connected class to enable connected layout styles
    app.classList.add('connected');
    tilesPanel.classList.remove('hidden');
    
    // Responsive layout
    if (window.innerWidth <= 768) {
      tilesPanel.classList.add('mobile-show');
    } else {
      // Set tiles panel width and adjust device panel
      tilesPanel.classList.add('w-3/4');
      devicePanel.classList.remove('w-full');
      devicePanel.classList.add('w-1/4');
    }
  }
}

function hideTilesPanel(): void {
  const tilesPanel = document.getElementById('tilesPanel');
  const devicePanel = document.getElementById('devicePanel');
  const app = document.getElementById('app');
  
  if (tilesPanel && devicePanel && app) {
    // Remove connected class to disable connected layout styles
    app.classList.remove('connected');
    tilesPanel.classList.add('hidden');
    tilesPanel.classList.remove('mobile-show', 'w-3/4');
    
    // Reset device panel to full width
    devicePanel.classList.remove('w-1/4');
    devicePanel.classList.add('w-full');
  }
}


function updateConnectionCount(): void {
  const countEl = document.getElementById('connectionCount');
  const count = connectedDevices.size;
  
  if (countEl) {
    countEl.textContent = `${count} 个连接`;
  }
  
  // Update grid layout based on number of tiles
  const tilesContainer = document.getElementById('tilesContainer');
  if (tilesContainer) {
    tilesContainer.className = tilesContainer.className.replace(/has-\d+|has-many/g, '');
    
    if (count <= 4) {
      tilesContainer.classList.add(`has-${count}`);
    } else {
      tilesContainer.classList.add('has-many');
    }
  }
}

function createTile(device: ConnectedDevice): void {
  const tilesContainer = document.getElementById('tilesContainer');
  if (!tilesContainer) return;
  
  const tileHtml = `
    <div id="${device.tileId}" class="sonar-tile">
      <div class="tile-header">
        <div class="flex items-center space-x-2">
          <span class="w-2 h-2 bg-green-400 rounded-full"></span>
          <span class="text-sm font-medium text-white">${device.deviceName}</span>
          <span class="text-xs text-gray-400">(${device.ip})</span>
        </div>
        <div class="flex items-center space-x-2">
          <button class="refresh-tile text-blue-400 hover:text-blue-300 text-sm" data-ip="${device.ip}" title="刷新">
            ⟳
          </button>
          <button class="close-tile text-red-400 hover:text-red-300 text-sm" data-ip="${device.ip}" title="关闭">
            ✕
          </button>
        </div>
      </div>
      <div class="tile-content">
        <div class="tile-loading hidden">
          <div class="text-center">
            <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-400 mx-auto mb-2"></div>
            <p class="text-gray-300 text-sm">连接中...</p>
          </div>
        </div>
        <div class="tile-error hidden">
          <div class="text-center">
            <p class="text-red-400 mb-2">连接失败</p>
            <p class="text-gray-400 text-sm">无法连接到声呐设备</p>
          </div>
        </div>
        <iframe class="tile-iframe hidden" sandbox="allow-scripts allow-same-origin allow-forms allow-top-navigation allow-popups allow-downloads"></iframe>
      </div>
    </div>
  `;
  
  tilesContainer.insertAdjacentHTML('beforeend', tileHtml);
  
  // Add event listeners
  const tile = document.getElementById(device.tileId);
  if (tile) {
    const refreshBtn = tile.querySelector('.refresh-tile');
    const closeBtn = tile.querySelector('.close-tile');
    
    if (refreshBtn) {
      refreshBtn.addEventListener('click', handleRefreshTile);
    }
    
    if (closeBtn) {
      closeBtn.addEventListener('click', handleCloseTile);
    }

    // Add debug button to open in external browser
    const debugBtn = document.createElement('button');
    debugBtn.className = 'px-2 py-1 text-xs bg-yellow-600 hover:bg-yellow-700 text-white rounded ml-2';
    debugBtn.textContent = '浏览器';
    debugBtn.title = '在外部浏览器中打开声呐设备网页进行调试';
    debugBtn.onclick = () => {
      const url = `http://${device.ip}/?autostart=yes`;
      console.log(`在外部浏览器中打开: ${url}`);
      window.open(url, '_blank');
    };

    const titleBar = tile.querySelector('.flex.items-center.justify-between');
    if (titleBar) {
      titleBar.appendChild(debugBtn);
    }
  }
}

function showTileLoading(tileId: string): void {
  const tile = document.getElementById(tileId);
  if (!tile) return;
  
  const loading = tile.querySelector('.tile-loading');
  const iframe = tile.querySelector('.tile-iframe');
  const error = tile.querySelector('.tile-error');
  
  if (loading) loading.classList.remove('hidden');
  if (iframe) iframe.classList.add('hidden');
  if (error) error.classList.add('hidden');
}

function showTileError(tileId: string): void {
  const tile = document.getElementById(tileId);
  if (!tile) return;
  
  const loading = tile.querySelector('.tile-loading');
  const iframe = tile.querySelector('.tile-iframe');
  const error = tile.querySelector('.tile-error');
  
  if (loading) loading.classList.add('hidden');
  if (iframe) iframe.classList.add('hidden');
  if (error) error.classList.remove('hidden');
}

function showTileIframe(tileId: string): void {
  const tile = document.getElementById(tileId);
  if (!tile) return;

  const loading = tile.querySelector('.tile-loading');
  const iframe = tile.querySelector('.tile-iframe') as HTMLIFrameElement;
  const error = tile.querySelector('.tile-error');

  if (loading) loading.classList.add('hidden');
  if (iframe) {
    iframe.classList.remove('hidden');

    // Add additional monitoring for iframe health
    const monitorIframe = () => {
      try {
        // Check if iframe is still responsive
        if (iframe.contentWindow) {
          console.log(`iframe 监控: ${tileId} 状态正常`);
        }
      } catch (e) {
        // CORS errors are expected for external content
        console.log(`iframe 监控: ${tileId} 正在加载外部内容 (CORS限制)`);
      }
    };

    // Monitor iframe health periodically
    setTimeout(monitorIframe, 2000);
    setTimeout(monitorIframe, 5000);
  }
  if (error) error.classList.add('hidden');
}

async function loadSonarTile(device: ConnectedDevice): Promise<void> {
  return new Promise((resolve, reject) => {
    const tile = document.getElementById(device.tileId);
    if (!tile) {
      reject(new Error('Tile element not found'));
      return;
    }

    const iframe = tile.querySelector('.tile-iframe') as HTMLIFrameElement;
    if (!iframe) {
      reject(new Error('Iframe element not found in tile'));
      return;
    }

    const url = `http://${device.ip}/?autostart=yes`;
    console.log(`尝试加载声呐设备网页: ${url}`);

    let timeout: NodeJS.Timeout;
    let fallbackTimeout: NodeJS.Timeout;
    let loadHandled = false;

    const handleSuccess = () => {
      if (loadHandled) return;
      loadHandled = true;

      if (timeout) clearTimeout(timeout);
      if (fallbackTimeout) clearTimeout(fallbackTimeout);
      console.log(`成功加载声呐设备网页: ${url}`);
      showTileIframe(device.tileId);

      // Force update the tile display state
      setTimeout(() => {
        const tile = document.getElementById(device.tileId);
        if (tile) {
          const iframe = tile.querySelector('.tile-iframe') as HTMLIFrameElement;
          const error = tile.querySelector('.tile-error');
          const loading = tile.querySelector('.tile-loading');

          console.log(`强制更新tile显示状态: ${device.tileId}`);
          console.log(`iframe hidden: ${iframe?.classList.contains('hidden')}`);
          console.log(`error hidden: ${error?.classList.contains('hidden')}`);
          console.log(`loading hidden: ${loading?.classList.contains('hidden')}`);

          // Ensure iframe is visible and others are hidden
          if (iframe) iframe.classList.remove('hidden');
          if (error) error.classList.add('hidden');
          if (loading) loading.classList.add('hidden');
        }
      }, 100);

      resolve();
    };

    const handleError = (errorMsg: string) => {
      if (loadHandled) return;
      loadHandled = true;

      if (timeout) clearTimeout(timeout);
      if (fallbackTimeout) clearTimeout(fallbackTimeout);
      console.log(`${errorMsg}: ${url}`);
      reject(new Error(errorMsg));
    };

    // Set timeout for complete failure
    timeout = setTimeout(() => {
      handleError('加载超时');
    }, 10000); // 10 second timeout

    // Handle successful loading
    iframe.onload = () => {
      console.log(`iframe onload 事件触发: ${url}`);

      // Additional check: verify iframe content
      setTimeout(() => {
        try {
          const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
          if (iframeDoc) {
            console.log(`iframe 文档标题: "${iframeDoc.title}"`);
            console.log(`iframe 文档URL: "${iframeDoc.URL}"`);
            console.log(`iframe 文档body存在: ${!!iframeDoc.body}`);

            // Check if the page has loaded properly
            if (iframeDoc.body && iframeDoc.body.children.length > 0) {
              console.log(`iframe 内容已加载，body有 ${iframeDoc.body.children.length} 个子元素`);
            } else {
              console.warn(`iframe 加载但内容为空`);
            }
          } else {
            console.log(`无法访问iframe文档 (可能是CORS限制，这是正常的)`);
          }
        } catch (e) {
          const errorMsg = e instanceof Error ? e.message : String(e);
          console.log(`检查iframe内容时出现CORS错误 (这是正常的):`, errorMsg);
        }
      }, 500);

      handleSuccess();
    };

    // Handle loading errors
    iframe.onerror = (error) => {
      console.log(`iframe onerror 事件触发: ${url}`, error);
      handleError('iframe加载错误');
    };

    // Since the main process has already verified the connection is successful,
    // and we can see MJPEG stream is working, we use a more aggressive fallback
    fallbackTimeout = setTimeout(() => {
      if (!loadHandled) {
        console.log(`主进程已验证连接成功，MJPEG流正常，强制显示iframe: ${url}`);
        handleSuccess();
      }
    }, 2000); // 2 second fallback - more aggressive

    // Set iframe source to start loading
    iframe.src = url;

    // Additional check: monitor iframe content loading
    const checkIframeContent = () => {
      try {
        // Try to access iframe document to see if it's loaded
        // This might fail due to CORS, but that's expected
        if (iframe.contentDocument || iframe.contentWindow) {
          console.log(`iframe 内容检测: 可能已加载`);
        }
      } catch (e) {
        // CORS error is expected and actually indicates the iframe is loading external content
        console.log(`iframe CORS 限制检测到，说明正在加载外部内容: ${url}`);
      }
    };

    // Check iframe content after a short delay
    setTimeout(checkIframeContent, 1000);
  });
}

async function disconnectFromDevice(ip: string): Promise<void> {
  const device = connectedDevices.get(ip);
  if (!device) return;
  
  try {
    await electronAPI.disconnectFromDevice(ip);
  } catch (error) {
    console.error('Error disconnecting:', error);
  }
  
  // Remove tile from DOM
  const tile = document.getElementById(device.tileId);
  if (tile) {
    tile.remove();
  }
  
  // Remove from connected devices
  connectedDevices.delete(ip);
  
  // Update connection count and layout
  updateConnectionCount();
  updateConnectButtonState(ip);
  
  // Hide tiles panel if no connections
  if (connectedDevices.size === 0) {
    hideTilesPanel();
  }
}

async function refreshTile(ip: string): Promise<void> {
  const device = connectedDevices.get(ip);
  if (!device) return;
  
  const tile = document.getElementById(device.tileId);
  if (!tile) return;
  
  const iframe = tile.querySelector('.tile-iframe') as HTMLIFrameElement;
  if (!iframe) return;
  
  showTileLoading(device.tileId);
  
  // Clear iframe and reload
  iframe.src = 'about:blank';
  
  setTimeout(async () => {
    try {
      await loadSonarTile(device);
    } catch (error) {
      console.error('Error refreshing tile:', error);
      showTileError(device.tileId);
    }
  }, 100);
}

function updateConnectButtonState(ip: string): void {
  const isConnected = connectedDevices.has(ip);
  const buttons = document.querySelectorAll(`.connect-btn[data-ip="${ip}"]`);
  
  buttons.forEach(button => {
    const btn = button as HTMLButtonElement;
    if (isConnected) {
      btn.textContent = '已连接';
      btn.disabled = true;
      btn.classList.add('bg-gray-500', 'cursor-not-allowed');
      btn.classList.remove('bg-gradient-to-r', 'from-green-600', 'to-green-700', 'hover:from-green-700', 'hover:to-green-800');
    } else {
      btn.textContent = '连接';
      btn.disabled = false;
      btn.classList.remove('bg-gray-500', 'cursor-not-allowed');
      btn.classList.add('bg-gradient-to-r', 'from-green-600', 'to-green-700', 'hover:from-green-700', 'hover:to-green-800');
    }
  });
}

async function handleCloseTile(event: Event): Promise<void> {
  const button = event.target as HTMLButtonElement;
  const ip = button.getAttribute('data-ip');
  
  if (ip) {
    await disconnectFromDevice(ip);
  }
}

async function handleRefreshTile(event: Event): Promise<void> {
  const button = event.target as HTMLButtonElement;
  const ip = button.getAttribute('data-ip');
  
  if (ip) {
    await refreshTile(ip);
  }
}

async function closeAllTiles(): Promise<void> {
  const ips = Array.from(connectedDevices.keys());
  
  for (const ip of ips) {
    await disconnectFromDevice(ip);
  }
}

document.addEventListener('DOMContentLoaded', () => {
  const refreshBtn = document.getElementById('refreshBtn');
  if (refreshBtn) {
    refreshBtn.addEventListener('click', refreshDevices);
  }
  
  // Add event listeners for tiles controls
  const closeAllTilesBtn = document.getElementById('closeAllTiles');
  
  if (closeAllTilesBtn) {
    closeAllTilesBtn.addEventListener('click', closeAllTiles);
  }
  
  // Handle window resize for responsive layout
  window.addEventListener('resize', () => {
    const tilesPanel = document.getElementById('tilesPanel');
    if (tilesPanel && !tilesPanel.classList.contains('hidden')) {
      if (window.innerWidth <= 768) {
        tilesPanel.classList.add('mobile-show');
      } else {
        tilesPanel.classList.remove('mobile-show');
      }
    }
  });

  setInterval(refreshDevices, 5000);
});